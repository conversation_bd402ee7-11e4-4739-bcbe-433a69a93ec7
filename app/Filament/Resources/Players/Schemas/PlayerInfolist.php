<?php

namespace App\Filament\Resources\Players\Schemas;

use Carbon\Carbon;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;

class PlayerInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Player Profile')
                    ->description('Basic player information and photo')
                    ->schema([
                        ImageEntry::make('image')
                            ->label('')
                            ->disk('public')
                            ->circular()
                            ->columnSpan(1),
                        TextEntry::make('name')
                            ->label('Player Name')
                            ->weight(FontWeight::Bold)
                            ->size('xl')
                            ->icon('heroicon-o-user')
                            ->columnSpan(2),
                        TextEntry::make('name_ar')
                            ->label('Player Name (Arabic)')
                            ->weight(FontWeight::Medium)
                            ->placeholder('Not specified')
                            ->icon('heroicon-o-language')
                            ->columnSpan(2),
                        TextEntry::make('position')
                            ->label('Position')
                            ->badge(),
                    ])
                    ->columns(3)
                    ->extraAttributes([
                        'class' => 'bg-gradient-to-br from-white to-gray-50 border-2 border-gray-200 rounded-xl shadow-lg p-6',
                    ]),

                Section::make('Personal Information')
                    ->description('Nationality and biographical details')
                    ->schema([
                        TextEntry::make('country')
                            ->label('Nationality')
                            ->placeholder('Unknown')
                            ->icon('heroicon-o-flag')
                            ->badge()
                            ->color('info'),
                        TextEntry::make('birthday')
                            ->label('Date of Birth')
                            ->date('M j, Y')
                            ->icon('heroicon-o-calendar-days'),
                        TextEntry::make('age')
                            ->label('Age')
                            ->state(function ($record) {
                                if ($record->birthday) {
                                    $birthday = is_string($record->birthday)
                                        ? Carbon::parse($record->birthday)
                                        : $record->birthday;

                                    return $birthday->age.' years old';
                                }

                                return 'Unknown';
                            })
                            ->icon('heroicon-o-clock')
                            ->badge()
                            ->color('success'),
                    ])
                    ->columns(3),

                Section::make('Team Information')
                    ->description('Current team and club details')
                    ->schema([
                        ImageEntry::make('current_team_logo')
                            ->label('Current Team Logo')
                            ->disk('public')
                            ->state(function ($record) {
                                // Set session for testing
                                $currentTeam = $record->currentTeam();

                                return $currentTeam ? $currentTeam->logo : 'No current team';
                            })
                            ->columnSpan(1),

                        TextEntry::make('current_team_name')
                            ->label('Current Team')
                            ->state(function ($record) {
                                // Set session for testing
                                $currentTeam = $record->currentTeam();

                                return $currentTeam ? $currentTeam->name : 'No current team';
                            })
                            ->placeholder('No current team')
                            ->icon('heroicon-o-building-office-2')
                            ->badge()
                            ->color('primary'),
                        TextEntry::make('teams_count')
                            ->label('Total Teams')
                            ->state(function ($record) {
                                return $record->teams()->count();
                            })
                            ->icon('heroicon-o-building-library')
                            ->badge()
                            ->color('info'),
                    ])
                    ->columns(4),

                Section::make('Fantasy Information')
                    ->description('Market value and fantasy statistics')
                    ->schema([
                        TextEntry::make('current_market_value')
                            ->label('Current Market Value')
                            ->state(function ($record) {
                                $marketValue = $record->getCurrentMarketValue();

                                return $marketValue ? '$'.number_format($marketValue / 10, 1).'M' : 'Not set';
                            })
                            ->placeholder('Not set')
                            ->icon('heroicon-o-currency-dollar')
                            ->badge()
                            ->color('success'),
                        TextEntry::make('fantasy_selections')
                            ->label('Fantasy Selections')
                            ->state(function ($record) {
                                return $record->fantasyPlayers()->count();
                            })
                            ->icon('heroicon-o-star')
                            ->badge()
                            ->color('warning'),
                        TextEntry::make('transfers_in')
                            ->label('Transfers In')
                            ->state(function ($record) {
                                return $record->inFantasyTransfers()->count();
                            })
                            ->icon('heroicon-o-arrow-down-circle')
                            ->badge()
                            ->color('info'),
                        TextEntry::make('transfers_out')
                            ->label('Transfers Out')
                            ->state(function ($record) {
                                return $record->outFantasyTransfers()->count();
                            })
                            ->icon('heroicon-o-arrow-up-circle')
                            ->badge()
                            ->color('gray'),
                    ])
                    ->columns(4),

                Section::make('Performance Statistics')
                    ->description('Season performance and statistics')
                    ->schema([
                        TextEntry::make('total_goals')
                            ->label('Goals')
                            ->state(function ($record) {
                                return $record->playerPerformances()->sum('goals_scored');
                            })
                            ->icon('heroicon-o-trophy')
                            ->badge()
                            ->color('success'),
                        TextEntry::make('total_assists')
                            ->label('Assists')
                            ->state(function ($record) {
                                return $record->playerPerformances()->sum('assists');
                            })
                            ->icon('heroicon-o-hand-thumb-up')
                            ->badge()
                            ->color('info'),
                        TextEntry::make('total_minutes')
                            ->label('Minutes Played')
                            ->state(function ($record) {
                                return number_format($record->playerPerformances()->sum('minutes_played'));
                            })
                            ->icon('heroicon-o-clock')
                            ->badge()
                            ->color('primary'),
                        TextEntry::make('yellow_cards')
                            ->label('Yellow Cards')
                            ->state(function ($record) {
                                return $record->playerPerformances()->sum('yellow_cards');
                            })
                            ->icon('heroicon-o-exclamation-triangle')
                            ->badge()
                            ->color('warning'),
                        TextEntry::make('red_cards')
                            ->label('Red Cards')
                            ->state(function ($record) {
                                return $record->playerPerformances()->sum('red_cards');
                            })
                            ->icon('heroicon-o-x-circle')
                            ->badge()
                            ->color('danger'),
                        TextEntry::make('clean_sheets')
                            ->label('Clean Sheets')
                            ->state(function ($record) {
                                return $record->playerPerformances()->where('clean_sheet', true)->count();
                            })
                            ->icon('heroicon-o-shield-check')
                            ->badge()
                            ->color('success')
                            ->visible(fn ($record) => $record->position->value === 'GK' || $record->position->value === 'DEF'),
                    ])
                    ->columns(3),

                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-plus-circle'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('M j, Y g:i A')
                            ->since()
                            ->icon('heroicon-o-pencil-square'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
