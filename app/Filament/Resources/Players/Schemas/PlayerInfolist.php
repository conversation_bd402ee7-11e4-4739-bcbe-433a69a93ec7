<?php

namespace App\Filament\Resources\Players\Schemas;

use Carbon\Carbon;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;

class PlayerInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Player Profile')
                    ->description('Basic player information and photo')
                    ->schema([
                        ImageEntry::make('image')
                            ->label('')
                            ->disk('public')
                            ->circular()
                            ->columnSpan(1),
                        TextEntry::make('name')
                            ->label('Player Name')
                            ->weight(FontWeight::Bold)
                            ->size('xl')
                            ->icon('heroicon-o-user')
                            ->columnSpan(2),
                        TextEntry::make('name_ar')
                            ->label('Player Name (Arabic)')
                            ->weight(FontWeight::Medium)
                            ->placeholder('Not specified')
                            ->icon('heroicon-o-language')
                            ->columnSpan(2),
                        TextEntry::make('position')
                            ->label('Position')
                            ->badge(),
                    ])
                    ->columns(3)
                    ->extraAttributes([
                        'class' => 'bg-gradient-to-br from-white to-gray-50 border-2 border-gray-200 rounded-xl shadow-lg p-6',
                    ]),

                Section::make('Personal Information')
                    ->description('Nationality and biographical details')az
                    ->schema([
                        TextEntry::make('country')
                            ->label('Nationality')
                            ->placeholder('Unknown')
                            ->icon('heroicon-o-flag')
                            ->badge()
                            ->color('info'),
                        TextEntry::make('birthday')
                            ->label('Date of Birth')
                            ->date('M j, Y')
                            ->icon('heroicon-o-calendar-days'),
                        TextEntry::make('age')
                            ->label('Age')
                            ->state(function ($record) {
                                if ($record->birthday) {
                                    $birthday = is_string($record->birthday)
                                        ? Carbon::parse($record->birthday)
                                        : $record->birthday;

                                    return $birthday->age.' years old';
                                }

                                return 'Unknown';
                            })
                            ->icon('heroicon-o-clock')
                            ->badge()
                            ->color('success'),
                    ])
                    ->columns(3),

                Section::make('System Information')
                    ->description('Record timestamps and metadata')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime('M j, Y g:i A')
                            ->icon('heroicon-o-plus-circle'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('M j, Y g:i A')
                            ->since()
                            ->icon('heroicon-o-pencil-square'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
