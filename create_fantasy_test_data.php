<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Creating fantasy test data for Player ID 1...\n\n";

// Get the player and tenant
$player = App\Models\Player::find(1);
$tenant = App\Models\Tenant::first();

if (! $player) {
    echo "❌ Player ID 1 not found!\n";
    exit(1);
}

if (! $tenant) {
    echo "❌ No tenant found!\n";
    exit(1);
}

echo "🎯 Target Player: {$player->name}\n";
echo "🏢 Tenant: {$tenant->name}\n\n";

// Get or create a season
$season = App\Models\Season::first();
if (! $season) {
    echo "❌ No season found. Please create a season first.\n";
    exit(1);
}

echo "📅 Season: {$season->name}\n";

// Create a user if none exists
$user = App\Models\User::first();
if (! $user) {
    $user = App\Models\User::create([
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
    ]);
    echo "👤 Created test user: {$user->first_name} {$user->last_name}\n";
} else {
    echo "👤 Using existing user: {$user->first_name} {$user->last_name}\n";
}

// Create fantasy teams
echo "\n🏆 Creating Fantasy Teams...\n";
$fantasyTeams = [];
for ($i = 1; $i <= 3; $i++) {
    $fantasyTeam = App\Models\FantasyTeam::create([
        'tenant_id' => $tenant->id,
        'user_id' => $user->id,
        'season_id' => $season->id,
        'name' => "Test Fantasy Team {$i}",
        'budget' => 1000,
        'total_points' => rand(50, 200),
    ]);
    $fantasyTeams[] = $fantasyTeam;
    echo "  ✅ Created: {$fantasyTeam->name}\n";
}

// Get or create season phase and gameweeks
echo "\n📊 Getting/Creating Season Phase and Gameweeks...\n";
$seasonPhase = App\Models\SeasonPhase::where('season_id', $season->id)->first();
if (! $seasonPhase) {
    $seasonPhase = App\Models\SeasonPhase::create([
        'season_id' => $season->id,
        'name' => 'Regular Season',
        'format' => 'league',
        'config' => [
            'rounds' => 38,
            'home_away' => true,
        ],
        'teams_count' => 20,
        'status' => 'ongoing',
    ]);
    echo "  ✅ Created season phase: {$seasonPhase->name}\n";
} else {
    echo "  ✅ Using existing season phase: {$seasonPhase->name}\n";
}

// Get existing gameweeks or create some
$gameweeks = App\Models\Gameweek::where('season_phase_id', $seasonPhase->id)->get();
if ($gameweeks->count() === 0) {
    echo "  📊 Creating gameweeks...\n";
    $gameweeks = collect();
    for ($i = 1; $i <= 5; $i++) {
        $gameweek = App\Models\Gameweek::create([
            'season_phase_id' => $seasonPhase->id,
            'name' => "Gameweek {$i}",
            'start_date' => now()->addWeeks($i - 1),
            'end_date' => now()->addWeeks($i)->subDay(),
            'rules' => [
                'transfers_allowed' => 2,
                'captain_multiplier' => 2,
                'vice_captain_multiplier' => 1.5,
            ],
            'status' => $i === 1 ? 'ongoing' : 'upcoming',
        ]);
        $gameweeks->push($gameweek);
        echo "    ✅ Created: {$gameweek->name}\n";
    }
} else {
    echo "  ✅ Using existing gameweeks: {$gameweeks->count()} found\n";
}

// Create market values for the player
echo "\n💰 Creating Market Values...\n";
foreach ($gameweeks as $index => $gameweek) {
    $marketValue = 50 + ($index * 5); // Increasing market value
    App\Models\PlayerMarketValue::create([
        'player_id' => $player->id,
        'gameweek_id' => $gameweek->id,
        'market_value' => $marketValue,
    ]);
    echo "  ✅ Gameweek {$gameweek->name}: \${$marketValue}M\n";
}

// Create fantasy player selections
echo "\n⭐ Creating Fantasy Player Selections...\n";
foreach ($fantasyTeams as $teamIndex => $fantasyTeam) {
    foreach ($gameweeks as $gwIndex => $gameweek) {
        // Only select player in some gameweeks for variety
        if (($teamIndex + $gwIndex) % 2 === 0) {
            App\Models\FantasyPlayer::create([
                'fantasy_team_id' => $fantasyTeam->id,
                'gameweek_id' => $gameweek->id,
                'player_id' => $player->id,
                'is_captain' => $gwIndex === 0, // Captain in first gameweek
                'is_vice_captain' => $gwIndex === 1, // Vice captain in second
                'purchase_price' => 50 + ($gwIndex * 3),
            ]);
            echo "  ✅ {$fantasyTeam->name} selected {$player->name} in {$gameweek->name}\n";
        }
    }
}

// Create fantasy transfers
echo "\n🔄 Creating Fantasy Transfers...\n";
$otherPlayer = App\Models\Player::where('id', '!=', $player->id)->first();

if ($otherPlayer) {
    foreach ($fantasyTeams as $teamIndex => $fantasyTeam) {
        // Transfer IN (player coming in)
        App\Models\FantasyTransfer::create([
            'tenant_id' => $tenant->id,
            'fantasy_team_id' => $fantasyTeam->id,
            'gameweek_id' => $gameweeks[1]->id,
            'player_in_id' => $player->id,
            'player_out_id' => $otherPlayer->id,
            'transfer_cost' => 4.0,
            'is_free_transfer' => false,
            'transfer_type' => 'in',
        ]);
        echo "  ✅ Transfer IN: {$player->name} to {$fantasyTeam->name}\n";

        // Transfer OUT (player going out)
        App\Models\FantasyTransfer::create([
            'tenant_id' => $tenant->id,
            'fantasy_team_id' => $fantasyTeam->id,
            'gameweek_id' => $gameweeks[3]->id,
            'player_in_id' => $otherPlayer->id,
            'player_out_id' => $player->id,
            'transfer_cost' => 0.0,
            'is_free_transfer' => true,
            'transfer_type' => 'out',
        ]);
        echo "  ✅ Transfer OUT: {$player->name} from {$fantasyTeam->name}\n";
    }
} else {
    echo "  ⚠️  No other player found for transfer data\n";
}

// Test the data
echo "\n🧪 Testing Fantasy Information Data...\n";
$currentMarketValue = $player->getCurrentMarketValue();
$fantasySelections = $player->fantasyPlayers()->count();
$transfersIn = $player->inFantasyTransfers()->count();
$transfersOut = $player->outFantasyTransfers()->count();

echo '✅ Current Market Value: '.($currentMarketValue ? '$'.number_format($currentMarketValue / 10, 1).'M' : 'Not set')."\n";
echo "✅ Fantasy Selections: {$fantasySelections}\n";
echo "✅ Transfers In: {$transfersIn}\n";
echo "✅ Transfers Out: {$transfersOut}\n";

echo "\n🎉 Fantasy test data created successfully!\n";
echo "\n📋 Summary:\n";
echo 'Fantasy Teams: '.count($fantasyTeams)."\n";
echo 'Gameweeks: '.count($gameweeks)."\n";
echo 'Market Values: '.App\Models\PlayerMarketValue::where('player_id', $player->id)->count()."\n";
echo "Fantasy Selections: {$fantasySelections}\n";
echo 'Total Transfers: '.($transfersIn + $transfersOut)."\n";

echo "\n🎯 Now test the PlayerInfolist Fantasy Information section!\n";
