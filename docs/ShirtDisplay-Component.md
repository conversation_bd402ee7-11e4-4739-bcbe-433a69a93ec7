# ShirtDisplay Infolist Component

## Overview

The `ShirtDisplay` component is a custom Filament infolist entry that displays team shirts with their logo, base color, pattern type, and pattern color. It renders an interactive SVG representation of the team's kit.

## Features

- Displays team shirt with logo overlay
- Shows base color, pattern type, and pattern color
- Responsive design with proper styling
- Works with both home kit and goalkeeper kit data
- Uses the same SVG template as the form ShirtCreator component

## Usage

### Basic Usage

```php
use App\Infolists\Components\ShirtDisplay;

ShirtDisplay::make('shirt')
    ->label('Home Kit')
    ->logoField('logo')
```

### In Team Infolist

```php
Section::make('Team Kits')
    ->description('Team uniform designs with logo and colors')
    ->schema([
        ShirtDisplay::make('shirt')
            ->label('Home Kit')
            ->logoField('logo')
            ->columnSpan(1),
        ShirtDisplay::make('gk_shirt')
            ->label('Goalkeeper Kit')
            ->logoField('logo')
            ->columnSpan(1),
    ])
    ->columns(2)
```

## Methods

### `logoField(string $field)`

Specifies which field contains the logo path for the team.

**Parameters:**
- `$field` - The field name that contains the logo path (e.g., 'logo')

**Example:**
```php
ShirtDisplay::make('shirt')
    ->logoField('logo')
```

## Data Structure

The component expects the shirt data to be stored as JSON with the following structure:

```json
{
    "base_color": "#FF0000",
    "pattern_type": "secondary_pattern_01",
    "pattern_color": "#FFFFFF"
}
```

### Fields:
- `base_color`: Hex color code for the shirt's base color
- `pattern_type`: Pattern identifier (e.g., "secondary_pattern_01" to "secondary_pattern_11", or empty for no pattern)
- `pattern_color`: Hex color code for the pattern color

## Requirements

- The SVG template must be located at `resources/js/Components/shirt-01.svg`
- Logo images should be stored in the public disk
- Alpine.js must be available for the interactive functionality

## Styling

The component includes:
- Responsive container with max width
- Gray background with border and shadow
- Centered shirt display
- Optional label below the shirt

## Browser Compatibility

- Requires modern browsers with SVG and Alpine.js support
- Uses DOMParser and XMLSerializer for SVG manipulation
